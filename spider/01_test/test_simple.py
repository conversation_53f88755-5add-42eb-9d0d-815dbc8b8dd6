import requests
import json

# 简化版本 - 不使用 PyMiniRacer，仅展示如何获取数据
def main():
    print("🚀 发送 HTTP 请求...")
    
    headers = {
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "zh-CN,zh;q=0.9",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
        "Content-Type": "application/x-www-form-urlencoded",
        "Origin": "https://wx.qmpsee.com",
        "Platform": "web",
        "Pragma": "no-cache",
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-site",
        "Source": "see",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
        "appflag": "see-h5-1.0.0",
        "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"macOS"',
    }

    data = {
        "page": "1",
        "num": "20",
        "ca_uuid": "feef62bfdac45a94b9cd89aed5c235be",
        "appflag": "see-h5-1.0.0",
    }

    try:
        response = requests.post(
            "https://wyiosapi.qmpsee.com/Web/getCaDetail", headers=headers, data=data
        )
        
        print(f"📡 HTTP 状态码: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print("📦 响应数据获取成功!")
            print("数据类型:", type(response_data))
            print("数据内容预览:")
            print(json.dumps(response_data, indent=2, ensure_ascii=False)[:500] + "...")
            
            # 检查 PyMiniRacer 是否已安装
            try:
                from py_mini_racer import MiniRacer
                print("\n✅ PyMiniRacer 已安装，可以运行完整版 test.py")
                print("💡 请运行: python test.py")
            except ImportError:
                print("\n❌ PyMiniRacer 未安装")
                print("💡 请先安装: pip install mini-racer")
                print("💡 然后运行: python test.py")
                
        else:
            print(f"❌ HTTP 请求失败，状态码: {response.status_code}")
            print("响应内容:", response.text)
            
    except Exception as e:
        print(f"❌ 请求过程中出错: {e}")

if __name__ == "__main__":
    main()
