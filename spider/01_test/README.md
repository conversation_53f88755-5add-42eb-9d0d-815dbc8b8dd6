# PyMiniRacer JavaScript 集成方案

## 🎯 项目说明

这个项目演示了如何在 Python 中使用 PyMiniRacer 执行 Node.js/JavaScript 代码，特别是调用 `encrypt_code.js` 中的 `Vc` 方法来处理 HTTP 响应数据。

## 📁 文件说明

- `test.py` - 完整版本，使用 PyMiniRacer 调用 JavaScript 函数
- `test_simple.py` - 简化版本，仅发送 HTTP 请求（用于测试环境）
- `encrypt_code.js` - JavaScript 加密/解密函数库
- `install_requirements.txt` - 依赖包安装说明

## 🚀 快速开始

### 第一步：安装依赖

```bash
# 安装 PyMiniRacer
pip install mini-racer

# 或者安装所有依赖
pip install -r install_requirements.txt
```

### 第二步：测试环境

```bash
# 先运行简化版本测试网络连接
python test_simple.py
```

### 第三步：运行完整版本

```bash
# 运行完整版本，调用 JavaScript 函数
python test.py
```

## 🔧 技术细节

### PyMiniRacer 优势
- ✅ 基于最新 V8 引擎
- ✅ 支持现代 JavaScript (ES6+)
- ✅ 线程安全
- ✅ 跨平台支持
- ✅ 2024年重新维护

### 集成流程
1. 加载 `encrypt_code.js` 文件
2. 初始化 PyMiniRacer 上下文
3. 发送 HTTP 请求获取数据
4. 调用 JavaScript `Vc` 方法处理数据
5. 返回处理结果

### Vc 方法参数说明
```javascript
Vc(e, t, n, o, a, r)
```
- `e`: 密钥字符串
- `t`: 要处理的数据
- `n`: 加密/解密标志 (0/1)
- `o`: 操作模式
- `a`: 初始化向量 (IV)
- `r`: 填充模式

## 🐛 故障排除

### 常见问题

1. **PyMiniRacer 安装失败**
   ```bash
   pip install --upgrade pip
   pip install mini-racer
   ```

2. **JavaScript 执行错误**
   - 检查 `encrypt_code.js` 文件是否存在
   - 确认 JavaScript 语法正确

3. **HTTP 请求失败**
   - 检查网络连接
   - 验证请求头和参数

## 📝 自定义使用

你可以根据需要修改以下参数：

```python
# 在 test.py 中修改 Vc 方法的参数
result = js_ctx.call("Vc", 
                   'your_key',           # 密钥
                   your_data,            # 数据
                   0,                    # 模式
                   0,                    # 选项
                   'your_iv',            # IV
                   1                     # 填充
                  )
```

## 🎉 完成

现在你可以在 Python 中轻松调用 JavaScript 函数了！
