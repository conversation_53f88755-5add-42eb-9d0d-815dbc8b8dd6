import json

# 示例数据（类似你的 API 响应）
sample_data = {
    "info": {
        "title": "消费行业研究",
        "description": "专注研究餐饮、家居生活等消费行业",
        "fans_num": "3"
    },
    "list": [
        {
            "news_title": "休闲零食行业研究分析报告",
            "content": "休闲食品行业赛道空间大，多年以来稳步增长。",
            "read_count": "2483"
        },
        {
            "news_title": "2024年中国健康家电消费洞察",
            "content": "当前，健康家电成为我国家电消费新趋势。",
            "read_count": "715"
        }
    ],
    "count": 2
}

print("🔍 json.dumps() 参数效果对比演示\n")

print("=" * 60)
print("1️⃣ 默认格式 - json.dumps(data)")
print("=" * 60)
result1 = json.dumps(sample_data)
print(result1)
print(f"长度: {len(result1)} 字符")

print("\n" + "=" * 60)
print("2️⃣ 添加缩进 - json.dumps(data, indent=2)")
print("=" * 60)
result2 = json.dumps(sample_data, indent=2)
print(result2)
print(f"长度: {len(result2)} 字符")

print("\n" + "=" * 60)
print("3️⃣ 完整格式 - json.dumps(data, indent=2, ensure_ascii=False)")
print("=" * 60)
result3 = json.dumps(sample_data, indent=2, ensure_ascii=False)
print(result3)
print(f"长度: {len(result3)} 字符")

print("\n" + "=" * 60)
print("4️⃣ 对比 ensure_ascii 的区别")
print("=" * 60)

# 创建包含中文的数据
chinese_data = {"消息": "你好世界", "状态": "成功"}

print("ensure_ascii=True (默认):")
print(json.dumps(chinese_data, indent=2, ensure_ascii=True))

print("\nensure_ascii=False:")
print(json.dumps(chinese_data, indent=2, ensure_ascii=False))

print("\n" + "=" * 60)
print("📝 总结")
print("=" * 60)
print("• indent=2: 让 JSON 格式化，便于阅读和调试")
print("• ensure_ascii=False: 让中文正常显示，不转换为 \\uxxxx")
print("• 在你的代码中用于美化输出，方便查看 API 响应数据")
