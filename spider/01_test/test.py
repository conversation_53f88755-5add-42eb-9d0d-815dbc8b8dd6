import requests
import json
from py_mini_racer import MiniRacer


# 读取 JavaScript 文件
def load_js_file(file_path):
    """加载 JavaScript 文件内容"""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            return f.read()
    except FileNotFoundError:
        print(f"错误：找不到文件 {file_path}")
        return None
    except Exception as e:
        print(f"读取文件时出错：{e}")
        return None


# 初始化 JavaScript 执行环境
def init_js_context():
    """初始化 PyMiniRacer 上下文并加载 JavaScript 代码"""
    ctx = MiniRacer()

    # 加载 encrypt_code.js
    js_code = load_js_file("encrypt_code.js")
    if js_code is None:
        return None

    try:
        # 执行 JavaScript 代码，定义函数
        ctx.eval(js_code)
        print("✅ JavaScript 代码加载成功")
        return ctx
    except Exception as e:
        print(f"❌ JavaScript 代码执行失败：{e}")
        return None


# HTTP 请求头
headers = {
    "Accept": "application/json, text/plain, */*",
    "Accept-Language": "zh-CN,zh;q=0.9",
    "Cache-Control": "no-cache",
    "Connection": "keep-alive",
    "Content-Type": "application/x-www-form-urlencoded",
    "Origin": "https://wx.qmpsee.com",
    "Platform": "web",
    "Pragma": "no-cache",
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-site",
    "Source": "see",
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "appflag": "see-h5-1.0.0",
    "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"macOS"',
}

# 请求数据
data = {
    "page": "1",
    "num": "20",
    "ca_uuid": "feef62bfdac45a94b9cd89aed5c235be",
    "appflag": "see-h5-1.0.0",
}

# 发送 HTTP 请求
print("🚀 发送 HTTP 请求...")
response = requests.post(
    "https://wyiosapi.qmpsee.com/Web/getCaDetail", headers=headers, data=data
)

print(f"📡 HTTP 状态码: {response.status_code}")

# 获取响应数据
response_data = response.json()
print("📦 原始响应数据:")
print(json.dumps(response_data, indent=2, ensure_ascii=False))

# 初始化 JavaScript 上下文
print("\n🔧 初始化 JavaScript 执行环境...")
js_ctx = init_js_context()

if js_ctx is not None:
    try:
        # 检查响应数据中是否有 encrypt_data 字段
        if "encrypt_data" in response_data:
            encrypt_data = response_data["encrypt_data"]
            print(f"\n🔐 找到加密数据，长度: {len(encrypt_data)}")
            print(f"加密数据预览: {encrypt_data[:100]}...")

            print(f"\n🔓 调用 Kc 方法解密数据...")

            # 调用 Kc 方法解密数据（这是正确的方法）
            # Kc 内部会调用 Vc 并用 JSON.parse 解析结果
            result = js_ctx.call("Kc", encrypt_data)

        else:
            print("\n❌ 响应数据中没有找到 encrypt_data 字段")
            print("📦 可用字段:", list(response_data.keys()))

            # 如果没有 encrypt_data，尝试直接处理整个响应
            response_json_str = json.dumps(response_data)
            print(f"\n🔐 尝试直接处理响应数据...")
            print(f"传入参数: {response_json_str[:100]}...")

            result = js_ctx.call(
                "Vc",
                "sjdqmp20161205#_316@gfmt",  # e: 密钥
                response_json_str,  # t: 要处理的数据
                0,  # n: 加密/解密标志
                0,  # o: 模式
                "012345677890123",  # a: IV
                1,  # r: 填充模式
            )

        print("✅ Vc 方法执行成功!")
        print("🎯 处理结果:")
        print(f"结果类型: {type(result)}")
        print(f"结果长度: {len(result) if hasattr(result, '__len__') else 'N/A'}")

        # 如果结果是字符串，显示前200个字符
        if isinstance(result, str):
            if len(result) > 200:
                print(f"结果预览: {result[:200]}...")
            else:
                print(f"完整结果: {result}")
        else:
            print(f"结果内容: {result}")

        # 尝试将结果保存到文件
        try:
            with open("vc_result.txt", "w", encoding="utf-8") as f:
                f.write(str(result))
            print("💾 结果已保存到 vc_result.txt 文件")
        except Exception as save_error:
            print(f"⚠️ 保存文件时出错: {save_error}")

    except Exception as e:
        print(f"❌ 调用 Vc 方法时出错：{e}")
        print("💡 提示：请检查参数是否正确")
else:
    print("❌ 无法初始化 JavaScript 环境，跳过 Vc 方法调用")
