import requests
import json
from py_mini_racer import MiniRacer

def test_decrypt():
    print("🔍 调试解密过程...")
    
    # 获取数据
    headers = {
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "zh-CN,zh;q=0.9",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
        "Content-Type": "application/x-www-form-urlencoded",
        "Origin": "https://wx.qmpsee.com",
        "Platform": "web",
        "Pragma": "no-cache",
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-site",
        "Source": "see",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
        "appflag": "see-h5-1.0.0",
        "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"macOS"',
    }

    data = {
        "page": "1",
        "num": "20",
        "ca_uuid": "feef62bfdac45a94b9cd89aed5c235be",
        "appflag": "see-h5-1.0.0",
    }

    response = requests.post(
        "https://wyiosapi.qmpsee.com/Web/getCaDetail", headers=headers, data=data
    )
    
    response_data = response.json()
    encrypt_data = response_data.get("encrypt_data", "")
    
    print(f"🔐 加密数据长度: {len(encrypt_data)}")
    print(f"🔐 加密数据前50字符: {encrypt_data[:50]}")
    
    # 加载 JavaScript
    with open('encrypt_code.js', 'r', encoding='utf-8') as f:
        js_code = f.read()
    
    ctx = MiniRacer()
    ctx.eval(js_code)
    
    # 测试不同的解密方法
    print("\n📝 测试1: 直接调用 Kc 方法")
    try:
        result1 = ctx.call("Kc", encrypt_data)
        print(f"结果类型: {type(result1)}")
        if isinstance(result1, str):
            print(f"结果长度: {len(result1)}")
            print(f"前100字符: {result1[:100]}")
            
            # 尝试解析为 JSON
            try:
                json_result = json.loads(result1)
                print("✅ 成功解析为 JSON!")
                print(f"JSON 类型: {type(json_result)}")
                if isinstance(json_result, dict):
                    print(f"JSON 键: {list(json_result.keys())}")
                return json_result
            except json.JSONDecodeError as e:
                print(f"❌ JSON 解析失败: {e}")
        else:
            print(f"结果内容: {result1}")
            
    except Exception as e:
        print(f"❌ Kc 方法调用失败: {e}")
    
    print("\n📝 测试2: 分步调用")
    try:
        # 先调用 decode
        decoded = ctx.call("decode", encrypt_data)
        print(f"decode 结果长度: {len(decoded)}")
        print(f"decode 前50字符: {decoded[:50]}")
        
        # 再调用 Vc
        vc_result = ctx.call("Vc", 'sjdqmp20161205#_316@gfmt', decoded, 0, 0, '012345677890123', 1)
        print(f"Vc 结果长度: {len(vc_result)}")
        print(f"Vc 前100字符: {vc_result[:100]}")
        
        # 尝试 JSON 解析
        try:
            json_result = json.loads(vc_result)
            print("✅ 分步调用成功解析为 JSON!")
            return json_result
        except json.JSONDecodeError as e:
            print(f"❌ 分步调用 JSON 解析失败: {e}")
            
    except Exception as e:
        print(f"❌ 分步调用失败: {e}")
    
    return None

if __name__ == "__main__":
    result = test_decrypt()
    if result:
        print(f"\n🎉 最终结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
